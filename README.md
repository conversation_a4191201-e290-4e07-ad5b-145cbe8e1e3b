# **佳宁工时预算原型系统 - 使用指南**

欢迎使用本系统！请按照以下步骤在您的电脑上运行本项目。

### **第一步：准备环境**

1. **安装 Python:** 请确保您的电脑上已经安装了 Python 3。您可以在命令行中输入 python --version 来检查。
2. **安装 VS Code:** 从 [Visual Studio Code 官网](https://www.google.com/url?sa=E&q=https%3A%2F%2Fcode.visualstudio.com%2F) 下载并安装。
3. **安装 VS Code Python 扩展:** 打开 VS Code，点击左侧的方块图标（扩展），搜索 Python 并安装由 **Microsoft** 发布的官方扩展。

### **第二步：设置项目**

1. **下载并解压项目:** 将项目文件夹（例如 WorkHour）放在您喜欢的位置。

2. **用 VS Code 打开项目:** 在 VS Code 中，选择 文件 > 打开文件夹...，然后选择您刚刚放置的项目文件夹。

3. **安装项目依赖:**

   - 按 Ctrl+`` (键盘左上角，数字1左边的键) 打开 VS Code 的集成终端。

   - 在终端中，输入并执行以下命令来安装本程序需要的所有库：

     Generated bash

     ```
     pip install -r requirements.txt
     ```

     Use code [with caution](https://support.google.com/legal/answer/13505487).Bash

   - **提示:** 此操作会将库安装到您的主Python环境中。对于此小型独立工具，这是最简单直接的方式。

### **第三步：运行与调试**

1. **直接运行:**
   - 在 VS Code 中，按 F5 键。
   - VS Code 可能会询问您如何调试，选择 **"Python"**，然后再次选择 **"Flask"**。它会自动帮您创建调试配置并启动程序。
   - 看到终端中出现类似 Running on http://127.0.0.1:5000 的信息，说明程序已成功运行。
2. **访问应用:** 按住 Ctrl 键并用鼠标点击终端中的 http://127.0.0.1:5000 链接，即可在浏览器中打开应用界面。
3. **如何调试:**
   - **设置断点:** 在 app.py 文件中，将鼠标移动到代码行的数字左侧，单击即可设置一个红点（断点）。
   - **开始调试:** 按 F5 启动调试。当程序执行到您的断点时，会自动暂停。
   - **观察与控制:** 您可以在左侧的“运行和调试”面板中查看变量的值，并使用顶部的工具栏（继续、单步跳过等）来控制程序的执行流程。

打包命令:

pyinstaller --onefile -w --name "佳宁工时预算" --icon="app.ico" --add-data "templates;templates" --add-data "static;static" app.py

### **第四步：去掉试用期功能：**

1. 打开 app.py 文件。
2. 找到 import license_validator 这一行。
3. 在行首加上 # 将其注释掉：# import license_validator。
4. 保存 app.py 文件。
5. **重新运行** pyinstaller 命令进行打包。