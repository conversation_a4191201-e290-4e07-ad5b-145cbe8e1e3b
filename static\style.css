body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
    background-color: #f4f7f9;
    color: #333;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1600px;
    margin: auto;
    background: #fff;
    padding: 20px 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

h1, h2 {
    color: #1a253c;
    border-bottom: 2px solid #eef2f5;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* 控制面板 */
.controls-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.control-group, .main-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* 参数设置 */
.settings-details {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
}
.settings-details summary {
    font-weight: bold;
    cursor: pointer;
}
.settings-content {
    margin-top: 15px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}
.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 表单元素和按钮 */
input[type="number"], input[type="date"], input[type="file"] {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}
input[type="file"] {
    max-width: 200px;
}

button {
    padding: 8px 16px;
    border: 1px solid #007bff;
    background-color: #fff;
    color: #007bff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease-in-out;
}
button:hover {
    background-color: #f0f8ff;
}

.primary-btn {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}
.primary-btn:hover {
    background-color: #0056b3;
}
#export-btn {
    border-color: #28a745;
    color: #28a745;
}
#export-btn:hover {
    background-color: #e9f5ec;
}
/* [FIX 1] 复位按钮的样式 */
.secondary-btn {
    border-color: #6c757d;
    color: #6c757d;
}
.secondary-btn:hover {
    background-color: #f8f9fa;
}

/* 月历网格 */
#calendar-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.month-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    background: #fdfdfd;
}

.month-title {
    text-align: center;
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 15px;
    color: #1a253c
}

.calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.day-header {
    font-weight: bold;
    text-align: center;
    padding-bottom: 5px;
    color: #007bff
}

.day-cell {
    border: 1px solid #eee;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    font-weight: bold;
}

.day-cell.empty {
    background-color: #f9f9f9;
    border-color: transparent;
}

.day-number {
    font-size: 0.9em;
    margin-bottom: 5px;
}

.day-cell input {
    width: 80%;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px 0;
    background-color: #f0f0f0;
    transition: background-color 0.3s;
    font-weight: 500;
}
.day-cell input:focus {
    outline: none;
    border: 1px solid #86b8ee;
    background-color: #fff;
}
.day-cell.weekend {
    background-color: #94c7e8; 
    border-radius: 4px;     
}
/* UX 优化样式 */
.day-cell.weekend .day-number, .day-cell.holiday .day-number {
    color: #034609;
    font-weight: bold;
    
}

input.is-zero {
    background-color: #a3e9a4; /* 绿色: 工时为0 */
}
input.is-special-work {
    background-color: #fed88f; /* 黄色: 周日/节假日加班 */
}
input.modified {
    background-color: #ffff00 !important; /* 淡黄色: 用户手动修改过，优先级最高 */
    border: 1px solid #f0e68c;
}


/* 汇总表格 */
#summary-container {
    overflow-x: auto;
}
#summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}
#summary-table th, #summary-table td {
    border: 1px solid #ddd;
    padding: 10px;
    /* [FIX 2] 居中对齐 */
    text-align: center; 
}
#summary-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}
#summary-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* 通知区域 */
#notification-area {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    display: none;
}
#notification-area.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}
#notification-area.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}