document.addEventListener('DOMContentLoaded', function() {
    const state = {
        holidays: new Set(),
        defaultHours: { weekday: 10, saturday: 8, special: 0 },
        dailyData: {},
    };
    const DOMElements = {
        startDate: document.getElementById('start-date'),
        endDate: document.getElementById('end-date'),
        calendarGrid: document.getElementById('calendar-grid'),
        summaryTableBody: document.querySelector('#summary-table tbody'),
        notificationArea: document.getElementById('notification-area'),
        defaultHourWeekday: document.getElementById('default-hour-weekday'),
        defaultHourSaturday: document.getElementById('default-hour-saturday'),
        defaultHourSpecial: document.getElementById('default-hour-special'),
        saveSettingsBtn: document.getElementById('save-settings-btn'),
        uploadHolidayBtn: document.getElementById('upload-holiday-btn'),
        calculateBtn: document.getElementById('calculate-btn'),
        exportBtn: document.getElementById('export-btn'),
        resetBtn: document.getElementById('reset-btn'),
        holidayFile: document.getElementById('holiday-file'),
    };

    async function initializeApp() {
        try {
            const response = await fetch('/api/get-initial-data');
            if (!response.ok) throw new Error('无法加载初始数据');
            const data = await response.json();
            
            DOMElements.defaultHourWeekday.value = data.parameters.default_hour_weekday || 10;
            DOMElements.defaultHourSaturday.value = data.parameters.default_hour_saturday || 8;
            DOMElements.defaultHourSpecial.value = data.parameters.default_hour_special || 0;
            updateDefaultHoursState();

            state.holidays = new Set(data.holidays || []);
            state.dailyData = data.daily_work_hours || {};
            
            if (data.parameters.start_date && data.parameters.end_date) {
                DOMElements.startDate.value = data.parameters.start_date;
                DOMElements.endDate.value = data.parameters.end_date;
            } else {
                setDefaultDateRange();
            }

            renderCalendars();
            renderSummaryTable({}); 

        } catch (error) {
            showNotification(`初始化失败: ${error.message}`, 'error');
            setDefaultDateRange();
            renderCalendars();
        }
    }

    function renderCalendars() {
        DOMElements.calendarGrid.innerHTML = '';
        const startStr = DOMElements.startDate.value;
        const endStr = DOMElements.endDate.value;
        if (!startStr || !endStr || startStr > endStr) {
            DOMElements.calendarGrid.innerHTML = '<p>请输入有效的日期范围。</p>';
            return;
        }
        const startYear = parseInt(startStr.substring(0, 4), 10);
        const startMonth = parseInt(startStr.substring(5, 7), 10) - 1;
        const end = new Date(Date.UTC(parseInt(endStr.substring(0, 4), 10), parseInt(endStr.substring(5, 7), 10) - 1, parseInt(endStr.substring(8, 10), 10)));
        const monthsToRender = [];
        let currentMonth = new Date(Date.UTC(startYear, startMonth, 1));
        while (currentMonth <= end) {
            monthsToRender.push(new Date(currentMonth));
            currentMonth.setUTCMonth(currentMonth.getUTCMonth() + 1);
            if (monthsToRender.length > 6) {
                DOMElements.calendarGrid.innerHTML = '<p class="error-message">日期范围过大，最多支持6个月的月历显示。</p>';
                break;
            }
        }
        if (monthsToRender.length < 4 && monthsToRender.length > 0) {
            DOMElements.calendarGrid.style.gridTemplateColumns = `repeat(${monthsToRender.length}, 1fr)`;
        } else if (monthsToRender.length >= 4) {
            DOMElements.calendarGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
        }
        monthsToRender.forEach(monthDate => {
            const monthContainer = document.createElement('div');
            monthContainer.className = 'month-container';
            const monthTitle = document.createElement('div');
            monthTitle.className = 'month-title';
            monthTitle.textContent = `${monthDate.getUTCFullYear()}年 ${monthDate.getUTCMonth() + 1}月`;
            const calendar = createMonthCalendar(monthDate.getUTCFullYear(), monthDate.getUTCMonth(), startStr, endStr);
            monthContainer.appendChild(monthTitle);
            monthContainer.appendChild(calendar);
            DOMElements.calendarGrid.appendChild(monthContainer);
        });
    }

    function createMonthCalendar(year, month, globalStartStr, globalEndStr) {
        const calendarDiv = document.createElement('div');
        calendarDiv.className = 'calendar';
        
        ['一', '二', '三', '四', '五', '六', '日'].forEach(day => {
            const header = document.createElement('div');
            header.className = 'day-header';
            header.textContent = day;
            calendarDiv.appendChild(header);
        });

        const firstDayOfMonth = new Date(Date.UTC(year, month, 1));
        const daysInMonth = new Date(Date.UTC(year, month + 1, 0)).getUTCDate();
        let startingDay = firstDayOfMonth.getUTCDay();
        if (startingDay === 0) startingDay = 7; 

        for (let i = 1; i < startingDay; i++) {
            calendarDiv.appendChild(document.createElement('div')).className = 'day-cell empty';
        }

        for (let day = 1; day <= daysInMonth; day++) {
            const currentDate = new Date(Date.UTC(year, month, day));
            const currentDateStr = currentDate.toISOString().split('T')[0];
            if (currentDateStr >= globalStartStr && currentDateStr <= globalEndStr) {
                const cell = createDayCell(currentDate, currentDateStr);
                calendarDiv.appendChild(cell);
            } else {
                const emptyCell = document.createElement('div');
                emptyCell.className = 'day-cell empty';
                calendarDiv.appendChild(emptyCell);
            }
        }
        return calendarDiv;
    }
    
    function createDayCell(date, dateString) {
        const cell = document.createElement('div');
        cell.className = 'day-cell';
        
        const dayNumber = document.createElement('span');
        dayNumber.className = 'day-number';
        dayNumber.textContent = date.getUTCDate();
        
        const hourInput = document.createElement('input');
        hourInput.type = 'number';
        hourInput.step = '0.5';
        hourInput.dataset.date = dateString;

        const dayOfWeek = date.getUTCDay();
        const isSpecialDay = dayOfWeek === 0 || state.holidays.has(dateString);

        if (dayOfWeek === 0 || dayOfWeek === 6) cell.classList.add('weekend');
        if (state.holidays.has(dateString)) cell.classList.add('holiday');
        
        const savedData = state.dailyData[dateString];
        if (savedData && savedData.is_modified) {
            hourInput.value = savedData.planned_hours;
            hourInput.classList.add('modified');
        } else {
            if (isSpecialDay) {
                hourInput.value = state.defaultHours.special;
            } else if (dayOfWeek === 6) {
                hourInput.value = state.defaultHours.saturday;
            } else {
                hourInput.value = state.defaultHours.weekday;
            }
        }

        const updateCellHighlight = () => {
            const plannedHours = parseFloat(hourInput.value);
            hourInput.classList.remove('is-zero', 'is-special-work');
            if (plannedHours === 0) {
                hourInput.classList.add('is-zero');
            } else if (isSpecialDay && plannedHours > 0) {
                hourInput.classList.add('is-special-work');
            }
        };
        
        hourInput.addEventListener('input', (e) => {
            hourInput.classList.add('modified');
            const date = e.target.dataset.date;
            state.dailyData[date] = state.dailyData[date] || {};
            state.dailyData[date].planned_hours = parseFloat(e.target.value);
            state.dailyData[date].is_modified = 1;
            updateCellHighlight();
        });

        cell.appendChild(dayNumber);
        cell.appendChild(hourInput);

        updateCellHighlight();
        return cell;
    }

    function renderSummaryTable(summaryData) { DOMElements.summaryTableBody.innerHTML = ''; const sortedMonths = Object.keys(summaryData).sort(); sortedMonths.forEach(monthKey => { const data = summaryData[monthKey]; const row = DOMElements.summaryTableBody.insertRow(); row.innerHTML = `<td>${data.month_key || monthKey}</td><td>${(data.normal_hours || 0).toFixed(2)}</td><td>${(data.normal_overtime || 0).toFixed(2)}</td><td>${(data.special_overtime || 0).toFixed(2)}</td>`; }); }
    
    // --- 事件处理函数  ---

    async function handleCalculate() { const allHourInputs = DOMElements.calendarGrid.querySelectorAll('input[data-date]'); const dailyHoursPayload = {}; allHourInputs.forEach(input => { const date = input.dataset.date; dailyHoursPayload[date] = { planned_hours: parseFloat(input.value), is_modified: input.classList.contains('modified') ? 1 : 0 }; }); const payload = { start_date: DOMElements.startDate.value, end_date: DOMElements.endDate.value, daily_hours: dailyHoursPayload }; try { const response = await fetch('/api/calculate', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) }); const result = await response.json(); if (result.status === 'success') { showNotification('核算成功！', 'success'); renderSummaryTable(result.summary); } else { showNotification(`核算失败: ${result.message}`, 'error'); } } catch (error) { showNotification(`核算请求失败: ${error.message}`, 'error'); } }
    async function handleSaveSettings() { const settings = { weekday: DOMElements.defaultHourWeekday.value, saturday: DOMElements.defaultHourSaturday.value, special: DOMElements.defaultHourSpecial.value }; try { const response = await fetch('/api/save-settings', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(settings) }); const result = await response.json(); showNotification(result.message, result.status); if (result.status === 'success') { updateDefaultHoursState(); renderCalendars(); } } catch (error) { showNotification(`保存失败: ${error.message}`, 'error'); } }
    async function handleUploadHolidays() { const file = DOMElements.holidayFile.files[0]; if (!file) { showNotification('请先选择一个文件', 'error'); return; } const formData = new FormData(); formData.append('holiday_file', file); try { const response = await fetch('/api/upload-holidays', { method: 'POST', body: formData }); const result = await response.json(); showNotification(result.message, result.status); if (result.status === 'success') { state.holidays = new Set(result.holidays); renderCalendars(); } } catch (error) { showNotification(`上传失败: ${error.message}`, 'error'); } }
    function handleExport() { window.location.href = '/api/export-excel'; }
    

    function handleReset() {
 
        state.dailyData = {};
        
        renderCalendars();

        renderSummaryTable({});

        showNotification('日历已恢复为默认状态。', 'success');
    }

    function setDefaultDateRange() { const today = new Date(); const nextMonthDate = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 1, 1)); const lastDayOfNextMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 2, 0)); DOMElements.startDate.value = nextMonthDate.toISOString().split('T')[0]; DOMElements.endDate.value = lastDayOfNextMonth.toISOString().split('T')[0]; }
    function showNotification(message, type = 'success') { DOMElements.notificationArea.textContent = message; DOMElements.notificationArea.className = `notification-area ${type}`; setTimeout(() => { DOMElements.notificationArea.className = 'notification-area'; }, 5000); }
    function updateDefaultHoursState() { state.defaultHours.weekday = parseFloat(DOMElements.defaultHourWeekday.value); state.defaultHours.saturday = parseFloat(DOMElements.defaultHourSaturday.value); state.defaultHours.special = parseFloat(DOMElements.defaultHourSpecial.value); }

    DOMElements.startDate.addEventListener('input', renderCalendars);
    DOMElements.endDate.addEventListener('input', renderCalendars);
    DOMElements.saveSettingsBtn.addEventListener('click', handleSaveSettings);
    DOMElements.uploadHolidayBtn.addEventListener('click', handleUploadHolidays);
    DOMElements.calculateBtn.addEventListener('click', handleCalculate);
    DOMElements.exportBtn.addEventListener('click', handleExport);
    DOMElements.resetBtn.addEventListener('click', handleReset);
    
    // --- 启动应用 ---
    initializeApp();
});